-- 创建彩色标签配置表
-- Author: Payne
-- Date: 2025-08-03
-- Description: 用于存储钱币彩色标签的配置信息

-- 删除表（如果存在）
DROP TABLE IF EXISTS COLOR_LABEL_CONFIG;

-- 创建彩色标签配置表
CREATE TABLE COLOR_LABEL_CONFIG (
    ID VARCHAR2(50) NOT NULL,
    COIN_ID VARCHAR2(50) NOT NULL,
    LABEL_TEXT VARCHAR2(200),
    TEXT_COLOR VARCHAR2(20) DEFAULT '#FF0000',
    FONT_SIZE NUMBER(3) DEFAULT 40,
    FONT_FAMILY VARCHAR2(100) DEFAULT '微软雅黑',
    FONT_WEIGHT VARCHAR2(20) DEFAULT 'normal',
    POSITION_X NUMBER(10,2) DEFAULT 0,
    POSITION_Y NUMBER(10,2) DEFAULT 0,
    WIDTH NUMBER(10,2) DEFAULT 45,
    HEIGHT NUMBER(10,2) DEFAULT 20,
    TEMPLATE_TYPE VARCHAR2(50),
    IS_ENABLED NUMBER(1) DEFAULT 1,
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREATE_USER VARCHAR2(50),
    UPDATE_USER VARCHAR2(50),
    REMARKS VARCHAR2(500),
    CONSTRAINT PK_COLOR_LABEL_CONFIG PRIMARY KEY (ID)
);

-- 创建索引
CREATE INDEX IDX_COLOR_LABEL_COIN_ID ON COLOR_LABEL_CONFIG(COIN_ID);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_TYPE ON COLOR_LABEL_CONFIG(TEMPLATE_TYPE);
CREATE INDEX IDX_COLOR_LABEL_ENABLED ON COLOR_LABEL_CONFIG(IS_ENABLED);
CREATE INDEX IDX_COLOR_LABEL_CREATE_TIME ON COLOR_LABEL_CONFIG(CREATE_TIME);

-- 添加表注释
COMMENT ON TABLE COLOR_LABEL_CONFIG IS '彩色标签配置表';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.ID IS '主键ID';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.COIN_ID IS '关联钱币ID';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.LABEL_TEXT IS '彩色标签文字内容';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.TEXT_COLOR IS '文字颜色(十六进制)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.FONT_SIZE IS '字体大小(px)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.FONT_FAMILY IS '字体名称';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.FONT_WEIGHT IS '字体粗细';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.POSITION_X IS 'X坐标位置(mm)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.POSITION_Y IS 'Y坐标位置(mm)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.WIDTH IS '标签宽度(mm)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.HEIGHT IS '标签高度(mm)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.TEMPLATE_TYPE IS '关联的钱币标签模板类型';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.IS_ENABLED IS '是否启用(1:启用,0:禁用)';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.CREATE_USER IS '创建用户';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.UPDATE_USER IS '更新用户';
COMMENT ON COLUMN COLOR_LABEL_CONFIG.REMARKS IS '备注说明';

-- 插入一些示例数据（可选）
INSERT INTO COLOR_LABEL_CONFIG (
    ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
    POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
    CREATE_TIME, CREATE_USER, REMARKS
) VALUES (
    'CLC001', 'COIN001', '68', '#FF0000', 40, '微软雅黑', 'bold',
    35.0, 6.2, 45.0, 20.0, '中乾模板', 1,
    CURRENT_TIMESTAMP, 'system', '示例彩色标签配置'
);

INSERT INTO COLOR_LABEL_CONFIG (
    ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
    POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
    CREATE_TIME, CREATE_USER, REMARKS
) VALUES (
    'CLC002', 'COIN002', '70', '#0066FF', 38, '微软雅黑', 'bold',
    37.5, 7.7, 45.0, 20.0, '金币模板', 1,
    CURRENT_TIMESTAMP, 'system', '示例彩色标签配置'
);

-- 提交事务
COMMIT;
