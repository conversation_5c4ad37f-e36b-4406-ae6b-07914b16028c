<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payne.server.banknote.mapper.ColorLabelConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.payne.server.banknote.entity.ColorLabelConfig">
        <id column="ID" property="id" />
        <result column="COIN_ID" property="coinId" />
        <result column="LABEL_TEXT" property="labelText" />
        <result column="TEXT_COLOR" property="textColor" />
        <result column="FONT_SIZE" property="fontSize" />
        <result column="FONT_FAMILY" property="fontFamily" />
        <result column="FONT_WEIGHT" property="fontWeight" />
        <result column="POSITION_X" property="positionX" />
        <result column="POSITION_Y" property="positionY" />
        <result column="WIDTH" property="width" />
        <result column="HEIGHT" property="height" />
        <result column="TEMPLATE_TYPE" property="templateType" />
        <result column="IS_ENABLED" property="isEnabled" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_USER" property="createUser" />
        <result column="UPDATE_USER" property="updateUser" />
        <result column="REMARKS" property="remarks" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
        POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARKS
    </sql>

    <!-- 根据钱币ID查询彩色标签配置 -->
    <select id="selectByCoinId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID = #{coinId}
        AND IS_ENABLED = 1
    </select>

    <!-- 根据钱币ID列表批量查询彩色标签配置 -->
    <select id="selectByCoinIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID IN
        <foreach collection="coinIds" item="coinId" open="(" separator="," close=")">
            #{coinId}
        </foreach>
        AND IS_ENABLED = 1
    </select>

    <!-- 根据模板类型查询彩色标签配置 -->
    <select id="selectByTemplateType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM COLOR_LABEL_CONFIG
        WHERE TEMPLATE_TYPE = #{templateType}
        AND IS_ENABLED = 1
        ORDER BY CREATE_TIME DESC
    </select>

    <!-- 批量插入彩色标签配置 -->
    <insert id="batchInsert">
        INSERT INTO COLOR_LABEL_CONFIG (
            ID, COIN_ID, LABEL_TEXT, TEXT_COLOR, FONT_SIZE, FONT_FAMILY, FONT_WEIGHT,
            POSITION_X, POSITION_Y, WIDTH, HEIGHT, TEMPLATE_TYPE, IS_ENABLED,
            CREATE_TIME, CREATE_USER, REMARKS
        ) VALUES
        <foreach collection="configs" item="config" separator=",">
            (
                #{config.id}, #{config.coinId}, #{config.labelText}, #{config.textColor},
                #{config.fontSize}, #{config.fontFamily}, #{config.fontWeight},
                #{config.positionX}, #{config.positionY}, #{config.width}, #{config.height},
                #{config.templateType}, #{config.isEnabled}, #{config.createTime},
                #{config.createUser}, #{config.remarks}
            )
        </foreach>
    </insert>

    <!-- 批量更新彩色标签配置 -->
    <update id="batchUpdate">
        <foreach collection="configs" item="config" separator=";">
            UPDATE COLOR_LABEL_CONFIG SET
                LABEL_TEXT = #{config.labelText},
                TEXT_COLOR = #{config.textColor},
                FONT_SIZE = #{config.fontSize},
                FONT_FAMILY = #{config.fontFamily},
                FONT_WEIGHT = #{config.fontWeight},
                POSITION_X = #{config.positionX},
                POSITION_Y = #{config.positionY},
                WIDTH = #{config.width},
                HEIGHT = #{config.height},
                TEMPLATE_TYPE = #{config.templateType},
                IS_ENABLED = #{config.isEnabled},
                UPDATE_TIME = #{config.updateTime},
                UPDATE_USER = #{config.updateUser},
                REMARKS = #{config.remarks}
            WHERE ID = #{config.id}
        </foreach>
    </update>

    <!-- 根据钱币ID删除彩色标签配置 -->
    <delete id="deleteByCoinId">
        DELETE FROM COLOR_LABEL_CONFIG WHERE COIN_ID = #{coinId}
    </delete>

    <!-- 根据钱币ID列表批量删除彩色标签配置 -->
    <delete id="deleteByCoinIds">
        DELETE FROM COLOR_LABEL_CONFIG
        WHERE COIN_ID IN
        <foreach collection="coinIds" item="coinId" open="(" separator="," close=")">
            #{coinId}
        </foreach>
    </delete>

</mapper>
