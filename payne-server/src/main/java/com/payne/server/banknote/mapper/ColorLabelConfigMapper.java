package com.payne.server.banknote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.server.banknote.entity.ColorLabelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 彩色标签配置Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Mapper
public interface ColorLabelConfigMapper extends BaseMapper<ColorLabelConfig> {
    
    /**
     * 根据钱币ID查询彩色标签配置
     *
     * @param coinId 钱币ID
     * @return 彩色标签配置
     */
    ColorLabelConfig selectByCoinId(@Param("coinId") String coinId);
    
    /**
     * 根据钱币ID列表批量查询彩色标签配置
     *
     * @param coinIds 钱币ID列表
     * @return 彩色标签配置列表
     */
    List<ColorLabelConfig> selectByCoinIds(@Param("coinIds") List<String> coinIds);
    
    /**
     * 根据模板类型查询彩色标签配置
     *
     * @param templateType 模板类型
     * @return 彩色标签配置列表
     */
    List<ColorLabelConfig> selectByTemplateType(@Param("templateType") String templateType);
    
    /**
     * 批量插入彩色标签配置
     *
     * @param configs 配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("configs") List<ColorLabelConfig> configs);
    
    /**
     * 批量更新彩色标签配置
     *
     * @param configs 配置列表
     * @return 更新数量
     */
    int batchUpdate(@Param("configs") List<ColorLabelConfig> configs);
    
    /**
     * 根据钱币ID删除彩色标签配置
     *
     * @param coinId 钱币ID
     * @return 删除数量
     */
    int deleteByCoinId(@Param("coinId") String coinId);
    
    /**
     * 根据钱币ID列表批量删除彩色标签配置
     *
     * @param coinIds 钱币ID列表
     * @return 删除数量
     */
    int deleteByCoinIds(@Param("coinIds") List<String> coinIds);
}
