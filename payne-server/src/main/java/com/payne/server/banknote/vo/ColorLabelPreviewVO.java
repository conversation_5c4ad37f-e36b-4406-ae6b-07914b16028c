package com.payne.server.banknote.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 彩色标签预览数据VO
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Data
public class ColorLabelPreviewVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 钱币ID
     */
    private String coinId;
    
    /**
     * 钱币编号
     */
    private String coinNumber;
    
    /**
     * 钱币名称
     */
    private String coinName;
    
    /**
     * 钱币标签数据
     */
    private CoinLabelData coinLabelData;
    
    /**
     * 彩色标签配置
     */
    private ColorLabelData colorLabelData;
    
    /**
     * 钱币标签数据
     */
    @Data
    public static class CoinLabelData implements Serializable {
        /**
         * 标签模板ID
         */
        private String templateId;
        
        /**
         * 标签模板名称
         */
        private String templateName;
        
        /**
         * 标签宽度 (mm)
         */
        private BigDecimal width;
        
        /**
         * 标签高度 (mm)
         */
        private BigDecimal height;
        
        /**
         * 标签HTML内容
         */
        private String htmlContent;
        
        /**
         * 标签字段数据
         */
        private Object fieldData;
    }
    
    /**
     * 彩色标签数据
     */
    @Data
    public static class ColorLabelData implements Serializable {
        /**
         * 配置ID
         */
        private String configId;
        
        /**
         * 标签文字
         */
        private String labelText;
        
        /**
         * 文字颜色
         */
        private String textColor;
        
        /**
         * 字体大小
         */
        private Integer fontSize;
        
        /**
         * 字体名称
         */
        private String fontFamily;
        
        /**
         * 字体粗细
         */
        private String fontWeight;
        
        /**
         * X坐标位置 (mm)
         */
        private BigDecimal positionX;
        
        /**
         * Y坐标位置 (mm)
         */
        private BigDecimal positionY;
        
        /**
         * 标签宽度 (mm)
         */
        private BigDecimal width;
        
        /**
         * 标签高度 (mm)
         */
        private BigDecimal height;
        
        /**
         * 是否启用
         */
        private Boolean isEnabled;
        
        /**
         * CSS样式字符串
         */
        private String cssStyle;
    }
}
