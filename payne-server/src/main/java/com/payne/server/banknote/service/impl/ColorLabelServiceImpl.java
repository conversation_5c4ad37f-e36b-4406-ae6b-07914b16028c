package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.server.banknote.entity.ColorLabelConfig;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.mapper.ColorLabelConfigMapper;
import com.payne.server.banknote.service.ColorLabelService;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.vo.ColorLabelPreviewVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 彩色标签服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Slf4j
@Service
public class ColorLabelServiceImpl extends ServiceImpl<ColorLabelConfigMapper, ColorLabelConfig> 
        implements ColorLabelService {
    
    @Autowired
    private ColorLabelConfigMapper colorLabelConfigMapper;
    
    @Autowired
    private PjOSendformItemService pjOSendformItemService;
    
    @Override
    public ColorLabelConfig getByCoinId(String coinId) {
        if (!StringUtils.hasText(coinId)) {
            return null;
        }
        return colorLabelConfigMapper.selectByCoinId(coinId);
    }
    
    @Override
    public Map<String, ColorLabelConfig> getByCoinIds(List<String> coinIds) {
        if (coinIds == null || coinIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<ColorLabelConfig> configs = colorLabelConfigMapper.selectByCoinIds(coinIds);
        return configs.stream()
                .collect(Collectors.toMap(ColorLabelConfig::getCoinId, config -> config));
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ColorLabelConfig saveConfig(ColorLabelConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("彩色标签配置不能为空");
        }
        
        // 设置默认值
        if (config.getTextColor() == null) {
            config.setTextColor("#FF0000");
        }
        if (config.getFontSize() == null) {
            config.setFontSize(40);
        }
        if (config.getFontFamily() == null) {
            config.setFontFamily("微软雅黑");
        }
        if (config.getFontWeight() == null) {
            config.setFontWeight("normal");
        }
        if (config.getIsEnabled() == null) {
            config.setIsEnabled(true);
        }
        
        if (config.getId() == null) {
            // 新增
            config.setCreateTime(LocalDateTime.now());
            config.setCreateUser("system"); // TODO: 从当前用户获取
            save(config);
        } else {
            // 更新
            config.setUpdateTime(LocalDateTime.now());
            config.setUpdateUser("system"); // TODO: 从当前用户获取
            updateById(config);
        }
        
        return config;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveConfigs(List<ColorLabelConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return 0;
        }
        
        // 分离新增和更新的配置
        List<ColorLabelConfig> insertConfigs = new ArrayList<>();
        List<ColorLabelConfig> updateConfigs = new ArrayList<>();
        
        for (ColorLabelConfig config : configs) {
            // 设置默认值
            setDefaultValues(config);
            
            if (config.getId() == null) {
                config.setCreateTime(LocalDateTime.now());
                config.setCreateUser("system");
                insertConfigs.add(config);
            } else {
                config.setUpdateTime(LocalDateTime.now());
                config.setUpdateUser("system");
                updateConfigs.add(config);
            }
        }
        
        int result = 0;
        if (!insertConfigs.isEmpty()) {
            result += colorLabelConfigMapper.batchInsert(insertConfigs);
        }
        if (!updateConfigs.isEmpty()) {
            result += colorLabelConfigMapper.batchUpdate(updateConfigs);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSetColorLabels(List<String> coinIds, String labelText, String textColor, 
                                  Integer fontSize, String fontFamily, String fontWeight, String templateType) {
        if (coinIds == null || coinIds.isEmpty()) {
            return 0;
        }
        
        List<ColorLabelConfig> configs = new ArrayList<>();
        for (String coinId : coinIds) {
            ColorLabelConfig config = getByCoinId(coinId);
            if (config == null) {
                config = new ColorLabelConfig();
                config.setCoinId(coinId);
            }
            
            config.setLabelText(labelText);
            config.setTextColor(textColor != null ? textColor : "#FF0000");
            config.setFontSize(fontSize != null ? fontSize : 40);
            config.setFontFamily(fontFamily != null ? fontFamily : "微软雅黑");
            config.setFontWeight(fontWeight != null ? fontWeight : "normal");
            config.setTemplateType(templateType);
            config.setIsEnabled(true);
            
            configs.add(config);
        }
        
        return batchSaveConfigs(configs);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyConfig(String sourceCoinId, List<String> targetCoinIds) {
        if (!StringUtils.hasText(sourceCoinId) || targetCoinIds == null || targetCoinIds.isEmpty()) {
            return 0;
        }
        
        ColorLabelConfig sourceConfig = getByCoinId(sourceCoinId);
        if (sourceConfig == null) {
            throw new RuntimeException("源钱币的彩色标签配置不存在");
        }
        
        List<ColorLabelConfig> configs = new ArrayList<>();
        for (String targetCoinId : targetCoinIds) {
            ColorLabelConfig config = new ColorLabelConfig();
            // 复制配置
            config.setCoinId(targetCoinId);
            config.setLabelText(sourceConfig.getLabelText());
            config.setTextColor(sourceConfig.getTextColor());
            config.setFontSize(sourceConfig.getFontSize());
            config.setFontFamily(sourceConfig.getFontFamily());
            config.setFontWeight(sourceConfig.getFontWeight());
            config.setPositionX(sourceConfig.getPositionX());
            config.setPositionY(sourceConfig.getPositionY());
            config.setWidth(sourceConfig.getWidth());
            config.setHeight(sourceConfig.getHeight());
            config.setTemplateType(sourceConfig.getTemplateType());
            config.setIsEnabled(sourceConfig.getIsEnabled());
            
            configs.add(config);
        }
        
        return batchSaveConfigs(configs);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfig(String coinId) {
        if (!StringUtils.hasText(coinId)) {
            return false;
        }
        return colorLabelConfigMapper.deleteByCoinId(coinId) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteConfigs(List<String> coinIds) {
        if (coinIds == null || coinIds.isEmpty()) {
            return 0;
        }
        return colorLabelConfigMapper.deleteByCoinIds(coinIds);
    }
    
    @Override
    public List<ColorLabelPreviewVO> generatePreviewData(List<String> coinIds, String templateId) {
        if (coinIds == null || coinIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<ColorLabelPreviewVO> previewList = new ArrayList<>();

        // 获取钱币信息
        List<PjOSendformItem> coins = pjOSendformItemService.listByIds(coinIds);
        Map<String, PjOSendformItem> coinMap = coins.stream()
                .collect(Collectors.toMap(PjOSendformItem::getId, coin -> coin));

        // 获取彩色标签配置
        Map<String, ColorLabelConfig> colorLabelMap = getByCoinIds(coinIds);

        for (String coinId : coinIds) {
            PjOSendformItem coin = coinMap.get(coinId);
            if (coin == null) continue;

            ColorLabelPreviewVO preview = new ColorLabelPreviewVO();
            preview.setCoinId(coinId);
            preview.setCoinNumber(coin.getSerialNumber());
            preview.setCoinName(coin.getCoinName1());

            // 设置钱币标签数据
            ColorLabelPreviewVO.CoinLabelData coinLabelData = new ColorLabelPreviewVO.CoinLabelData();
            coinLabelData.setTemplateId(templateId);
            coinLabelData.setTemplateName("标准模板"); // TODO: 从模板服务获取
            coinLabelData.setWidth(new BigDecimal("130")); // 130mm
            coinLabelData.setHeight(new BigDecimal("26"));  // 26mm
            coinLabelData.setFieldData(coin); // 钱币字段数据
            preview.setCoinLabelData(coinLabelData);

            // 设置彩色标签数据
            ColorLabelConfig colorConfig = colorLabelMap.get(coinId);
            if (colorConfig != null && colorConfig.getIsEnabled()) {
                ColorLabelPreviewVO.ColorLabelData colorLabelData = new ColorLabelPreviewVO.ColorLabelData();
                colorLabelData.setConfigId(colorConfig.getId());
                colorLabelData.setLabelText(colorConfig.getLabelText());
                colorLabelData.setTextColor(colorConfig.getTextColor());
                colorLabelData.setFontSize(colorConfig.getFontSize());
                colorLabelData.setFontFamily(colorConfig.getFontFamily());
                colorLabelData.setFontWeight(colorConfig.getFontWeight());
                colorLabelData.setPositionX(colorConfig.getPositionX());
                colorLabelData.setPositionY(colorConfig.getPositionY());
                colorLabelData.setWidth(colorConfig.getWidth());
                colorLabelData.setHeight(colorConfig.getHeight());
                colorLabelData.setIsEnabled(colorConfig.getIsEnabled());

                // 生成CSS样式
                String cssStyle = generateCssStyle(colorConfig);
                colorLabelData.setCssStyle(cssStyle);

                preview.setColorLabelData(colorLabelData);
            }

            previewList.add(preview);
        }

        return previewList;
    }
    
    @Override
    public Map<String, Object> generateIntegratedPrintData(List<String> coinIds, String templateId) {
        if (coinIds == null || coinIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Object> printData = new HashMap<>();

        // 生成预览数据作为打印数据的基础
        List<ColorLabelPreviewVO> previewData = generatePreviewData(coinIds, templateId);

        // 构建hiprint格式的打印数据
        List<Map<String, Object>> hiprintData = new ArrayList<>();

        for (ColorLabelPreviewVO preview : previewData) {
            Map<String, Object> itemData = new HashMap<>();

            // 钱币基础数据
            itemData.put("coinId", preview.getCoinId());
            itemData.put("coinNumber", preview.getCoinNumber());
            itemData.put("coinName", preview.getCoinName());

            // 钱币标签字段数据
            if (preview.getCoinLabelData() != null) {
                itemData.put("coinLabelData", preview.getCoinLabelData().getFieldData());
            }

            // 彩色标签数据
            if (preview.getColorLabelData() != null && preview.getColorLabelData().getIsEnabled()) {
                Map<String, Object> colorLabel = new HashMap<>();
                colorLabel.put("text", preview.getColorLabelData().getLabelText());
                colorLabel.put("color", preview.getColorLabelData().getTextColor());
                colorLabel.put("fontSize", preview.getColorLabelData().getFontSize());
                colorLabel.put("fontFamily", preview.getColorLabelData().getFontFamily());
                colorLabel.put("fontWeight", preview.getColorLabelData().getFontWeight());
                colorLabel.put("left", preview.getColorLabelData().getPositionX());
                colorLabel.put("top", preview.getColorLabelData().getPositionY());
                colorLabel.put("width", preview.getColorLabelData().getWidth());
                colorLabel.put("height", preview.getColorLabelData().getHeight());
                colorLabel.put("cssStyle", preview.getColorLabelData().getCssStyle());

                itemData.put("colorLabel", colorLabel);
            }

            hiprintData.add(itemData);
        }

        printData.put("items", hiprintData);
        printData.put("totalCount", hiprintData.size());
        printData.put("templateId", templateId);
        printData.put("printType", "integrated"); // 一体化打印类型
        printData.put("hasColorLabels", hiprintData.stream().anyMatch(item -> item.containsKey("colorLabel")));

        return printData;
    }
    
    @Override
    public ColorLabelConfig getDefaultConfig(String templateType) {
        ColorLabelConfig config = new ColorLabelConfig();
        setDefaultValues(config);
        config.setTemplateType(templateType);
        
        // 根据模板类型设置推荐位置
        Map<String, Object> position = getRecommendedPosition(templateType);
        if (position != null) {
            config.setPositionX(new BigDecimal(position.get("x").toString()));
            config.setPositionY(new BigDecimal(position.get("y").toString()));
        }
        
        return config;
    }
    
    @Override
    public Map<String, Object> getRecommendedPosition(String templateType) {
        Map<String, Object> position = new HashMap<>();
        
        // 根据不同模板类型返回推荐位置
        switch (templateType != null ? templateType : "default") {
            case "中乾模板":
                position.put("x", 35.0);  // mm
                position.put("y", 6.2);   // mm
                break;
            case "金币模板":
                position.put("x", 37.5);  // mm
                position.put("y", 7.7);   // mm
                break;
            default:
                position.put("x", 35.0);  // mm
                position.put("y", 1.5);   // mm
                break;
        }
        
        return position;
    }
    
    /**
     * 设置默认值
     */
    private void setDefaultValues(ColorLabelConfig config) {
        if (config.getTextColor() == null) {
            config.setTextColor("#FF0000");
        }
        if (config.getFontSize() == null) {
            config.setFontSize(40);
        }
        if (config.getFontFamily() == null) {
            config.setFontFamily("微软雅黑");
        }
        if (config.getFontWeight() == null) {
            config.setFontWeight("normal");
        }
        if (config.getPositionX() == null) {
            config.setPositionX(BigDecimal.ZERO);
        }
        if (config.getPositionY() == null) {
            config.setPositionY(BigDecimal.ZERO);
        }
        if (config.getWidth() == null) {
            config.setWidth(new BigDecimal("45"));
        }
        if (config.getHeight() == null) {
            config.setHeight(new BigDecimal("20"));
        }
        if (config.getIsEnabled() == null) {
            config.setIsEnabled(true);
        }
    }

    /**
     * 生成CSS样式字符串
     */
    private String generateCssStyle(ColorLabelConfig config) {
        StringBuilder css = new StringBuilder();
        css.append("position: absolute; ");
        css.append("left: ").append(config.getPositionX()).append("mm; ");
        css.append("top: ").append(config.getPositionY()).append("mm; ");
        css.append("width: ").append(config.getWidth()).append("mm; ");
        css.append("height: ").append(config.getHeight()).append("mm; ");
        css.append("color: ").append(config.getTextColor()).append("; ");
        css.append("font-size: ").append(config.getFontSize()).append("px; ");
        css.append("font-family: ").append(config.getFontFamily()).append("; ");
        css.append("font-weight: ").append(config.getFontWeight()).append("; ");
        css.append("display: flex; ");
        css.append("align-items: center; ");
        css.append("justify-content: center; ");
        css.append("z-index: 10; ");
        return css.toString();
    }
}
