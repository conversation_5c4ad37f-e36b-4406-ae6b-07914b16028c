package com.payne.server.banknote.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.server.banknote.entity.ColorLabelConfig;
import com.payne.server.banknote.vo.ColorLabelPreviewVO;

import java.util.List;
import java.util.Map;

/**
 * 彩色标签服务接口
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
public interface ColorLabelService extends IService<ColorLabelConfig> {
    
    /**
     * 根据钱币ID获取彩色标签配置
     *
     * @param coinId 钱币ID
     * @return 彩色标签配置
     */
    ColorLabelConfig getByCoinId(String coinId);
    
    /**
     * 根据钱币ID列表批量获取彩色标签配置
     *
     * @param coinIds 钱币ID列表
     * @return 彩色标签配置映射 (coinId -> config)
     */
    Map<String, ColorLabelConfig> getByCoinIds(List<String> coinIds);
    
    /**
     * 保存彩色标签配置
     *
     * @param config 彩色标签配置
     * @return 保存后的配置
     */
    ColorLabelConfig saveConfig(ColorLabelConfig config);
    
    /**
     * 批量保存彩色标签配置
     *
     * @param configs 配置列表
     * @return 保存成功的数量
     */
    int batchSaveConfigs(List<ColorLabelConfig> configs);
    
    /**
     * 批量设置彩色标签
     *
     * @param coinIds 钱币ID列表
     * @param labelText 标签文字
     * @param textColor 文字颜色
     * @param fontSize 字体大小
     * @param fontFamily 字体名称
     * @param fontWeight 字体粗细
     * @param templateType 模板类型
     * @return 设置成功的数量
     */
    int batchSetColorLabels(List<String> coinIds, String labelText, String textColor, 
                           Integer fontSize, String fontFamily, String fontWeight, String templateType);
    
    /**
     * 复制彩色标签配置
     *
     * @param sourceCoinId 源钱币ID
     * @param targetCoinIds 目标钱币ID列表
     * @return 复制成功的数量
     */
    int copyConfig(String sourceCoinId, List<String> targetCoinIds);
    
    /**
     * 删除彩色标签配置
     *
     * @param coinId 钱币ID
     * @return 是否删除成功
     */
    boolean deleteConfig(String coinId);
    
    /**
     * 批量删除彩色标签配置
     *
     * @param coinIds 钱币ID列表
     * @return 删除成功的数量
     */
    int batchDeleteConfigs(List<String> coinIds);
    
    /**
     * 生成彩色标签预览数据
     *
     * @param coinIds 钱币ID列表
     * @param templateId 钱币标签模板ID
     * @return 预览数据
     */
    List<ColorLabelPreviewVO> generatePreviewData(List<String> coinIds, String templateId);
    
    /**
     * 生成一体化打印数据 (钱币标签 + 彩色标签)
     *
     * @param coinIds 钱币ID列表
     * @param templateId 钱币标签模板ID
     * @return 打印数据
     */
    Map<String, Object> generateIntegratedPrintData(List<String> coinIds, String templateId);
    
    /**
     * 获取默认彩色标签配置
     *
     * @param templateType 模板类型
     * @return 默认配置
     */
    ColorLabelConfig getDefaultConfig(String templateType);
    
    /**
     * 根据模板类型获取推荐的彩色标签位置
     *
     * @param templateType 模板类型
     * @return 推荐位置配置
     */
    Map<String, Object> getRecommendedPosition(String templateType);
}
