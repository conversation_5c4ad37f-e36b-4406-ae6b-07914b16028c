package com.payne.server.banknote.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.server.banknote.entity.ColorLabelConfig;
import com.payne.server.banknote.service.ColorLabelService;
import com.payne.server.banknote.vo.ColorLabelPreviewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 彩色标签控制器
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@RestController
@RequestMapping("/api/color-label")
public class ColorLabelController extends BaseController {
    
    @Autowired
    private ColorLabelService colorLabelService;
    
    /**
     * 根据钱币ID获取彩色标签配置
     */
    @GetMapping("/config/{coinId}")
    public ApiResult<?> getConfig(@PathVariable String coinId) {
        try {
            ColorLabelConfig config = colorLabelService.getByCoinId(coinId);
            return success(config);
        } catch (Exception e) {
            return fail("获取配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量获取彩色标签配置
     */
    @PostMapping("/config/batch")
    public ApiResult<?> getBatchConfigs(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            Map<String, ColorLabelConfig> configs = colorLabelService.getByCoinIds(coinIds);
            return success(configs);
        } catch (Exception e) {
            return fail("批量获取配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存彩色标签配置
     */
    @PreAuthorize("hasAuthority('banknote:color-label:save')")
    @OperationLog(module = "彩色标签", comments = "保存彩色标签配置")
    @PostMapping("/config")
    public ApiResult<?> saveConfig(@RequestBody ColorLabelConfig config) {
        try {
            if (config.getCoinId() == null || config.getCoinId().trim().isEmpty()) {
                return fail("钱币ID不能为空");
            }
            
            ColorLabelConfig savedConfig = colorLabelService.saveConfig(config);
            return success("保存成功", savedConfig);
        } catch (Exception e) {
            return fail("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新彩色标签配置
     */
    @PreAuthorize("hasAuthority('banknote:color-label:save')")
    @OperationLog(module = "彩色标签", comments = "更新彩色标签配置")
    @PutMapping("/config/{id}")
    public ApiResult<?> updateConfig(@PathVariable String id, @RequestBody ColorLabelConfig config) {
        try {
            config.setId(id);
            ColorLabelConfig updatedConfig = colorLabelService.saveConfig(config);
            return success("更新成功", updatedConfig);
        } catch (Exception e) {
            return fail("更新失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除彩色标签配置
     */
    @PreAuthorize("hasAuthority('banknote:color-label:delete')")
    @OperationLog(module = "彩色标签", comments = "删除彩色标签配置")
    @DeleteMapping("/config/{coinId}")
    public ApiResult<?> deleteConfig(@PathVariable String coinId) {
        try {
            boolean result = colorLabelService.deleteConfig(coinId);
            if (result) {
                return success("删除成功");
            } else {
                return fail("删除失败");
            }
        } catch (Exception e) {
            return fail("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量设置彩色标签
     */
    @PreAuthorize("hasAuthority('banknote:color-label:batch')")
    @OperationLog(module = "彩色标签", comments = "批量设置彩色标签")
    @PostMapping("/batch-set")
    public ApiResult<?> batchSetColorLabels(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            String labelText = (String) params.get("labelText");
            String textColor = (String) params.get("textColor");
            Integer fontSize = parseInteger(params.get("fontSize"));
            String fontFamily = (String) params.get("fontFamily");
            String fontWeight = (String) params.get("fontWeight");
            String templateType = (String) params.get("templateType");
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            int result = colorLabelService.batchSetColorLabels(
                coinIds, labelText, textColor, fontSize, fontFamily, fontWeight, templateType);
            
            return success("批量设置成功，共处理 " + result + " 条记录");
        } catch (Exception e) {
            return fail("批量设置失败：" + e.getMessage());
        }
    }
    
    /**
     * 复制彩色标签配置
     */
    @PreAuthorize("hasAuthority('banknote:color-label:copy')")
    @OperationLog(module = "彩色标签", comments = "复制彩色标签配置")
    @PostMapping("/copy")
    public ApiResult<?> copyConfig(@RequestBody Map<String, Object> params) {
        try {
            String sourceCoinId = (String) params.get("sourceCoinId");
            @SuppressWarnings("unchecked")
            List<String> targetCoinIds = (List<String>) params.get("targetCoinIds");
            
            if (sourceCoinId == null || sourceCoinId.trim().isEmpty()) {
                return fail("源钱币ID不能为空");
            }
            if (targetCoinIds == null || targetCoinIds.isEmpty()) {
                return fail("目标钱币ID列表不能为空");
            }
            
            int result = colorLabelService.copyConfig(sourceCoinId, targetCoinIds);
            return success("复制成功，共处理 " + result + " 条记录");
        } catch (Exception e) {
            return fail("复制失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成预览数据
     */
    @PostMapping("/preview")
    public ApiResult<?> generatePreview(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            String templateId = (String) params.get("templateId");
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            List<ColorLabelPreviewVO> previewData = colorLabelService.generatePreviewData(coinIds, templateId);
            return success(previewData);
        } catch (Exception e) {
            return fail("生成预览失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成一体化打印数据
     */
    @PostMapping("/integrated-print")
    public ApiResult<?> generateIntegratedPrintData(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            String templateId = (String) params.get("templateId");
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            Map<String, Object> printData = colorLabelService.generateIntegratedPrintData(coinIds, templateId);
            return success(printData);
        } catch (Exception e) {
            return fail("生成打印数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取默认配置
     */
    @GetMapping("/default-config")
    public ApiResult<?> getDefaultConfig(@RequestParam(required = false) String templateType) {
        try {
            ColorLabelConfig defaultConfig = colorLabelService.getDefaultConfig(templateType);
            return success(defaultConfig);
        } catch (Exception e) {
            return fail("获取默认配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取推荐位置
     */
    @GetMapping("/recommended-position")
    public ApiResult<?> getRecommendedPosition(@RequestParam(required = false) String templateType) {
        try {
            Map<String, Object> position = colorLabelService.getRecommendedPosition(templateType);
            return success(position);
        } catch (Exception e) {
            return fail("获取推荐位置失败：" + e.getMessage());
        }
    }
    
    /**
     * 安全解析整数值
     */
    private Integer parseInteger(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        return null;
    }
}
