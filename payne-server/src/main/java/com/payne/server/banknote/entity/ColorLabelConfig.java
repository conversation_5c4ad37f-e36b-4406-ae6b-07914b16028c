package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 彩色标签配置实体类
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Entity
@Table(name = "COLOR_LABEL_CONFIG")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("COLOR_LABEL_CONFIG")
public class ColorLabelConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 关联钱币ID
     */
    @Column(name = "COIN_ID", columnDefinition = "VARCHAR2(50)")
    @TableField("COIN_ID")
    private String coinId;
    
    /**
     * 彩色标签文字内容
     */
    @Column(name = "LABEL_TEXT", columnDefinition = "VARCHAR2(200)")
    @TableField("LABEL_TEXT")
    private String labelText;
    
    /**
     * 文字颜色 (十六进制颜色值)
     */
    @Column(name = "TEXT_COLOR", columnDefinition = "VARCHAR2(20)")
    @TableField("TEXT_COLOR")
    private String textColor = "#FF0000";
    
    /**
     * 字体大小 (px)
     */
    @Column(name = "FONT_SIZE", columnDefinition = "NUMBER(3)")
    @TableField("FONT_SIZE")
    private Integer fontSize = 40;
    
    /**
     * 字体名称
     */
    @Column(name = "FONT_FAMILY", columnDefinition = "VARCHAR2(100)")
    @TableField("FONT_FAMILY")
    private String fontFamily = "微软雅黑";
    
    /**
     * 字体粗细
     */
    @Column(name = "FONT_WEIGHT", columnDefinition = "VARCHAR2(20)")
    @TableField("FONT_WEIGHT")
    private String fontWeight = "normal";
    
    /**
     * X坐标位置 (mm)
     */
    @Column(name = "POSITION_X", columnDefinition = "NUMBER(10,2)")
    @TableField("POSITION_X")
    private BigDecimal positionX = BigDecimal.ZERO;
    
    /**
     * Y坐标位置 (mm)
     */
    @Column(name = "POSITION_Y", columnDefinition = "NUMBER(10,2)")
    @TableField("POSITION_Y")
    private BigDecimal positionY = BigDecimal.ZERO;
    
    /**
     * 标签宽度 (mm)
     */
    @Column(name = "WIDTH", columnDefinition = "NUMBER(10,2)")
    @TableField("WIDTH")
    private BigDecimal width = new BigDecimal("45");
    
    /**
     * 标签高度 (mm)
     */
    @Column(name = "HEIGHT", columnDefinition = "NUMBER(10,2)")
    @TableField("HEIGHT")
    private BigDecimal height = new BigDecimal("20");
    
    /**
     * 关联的钱币标签模板类型
     */
    @Column(name = "TEMPLATE_TYPE", columnDefinition = "VARCHAR2(50)")
    @TableField("TEMPLATE_TYPE")
    private String templateType;
    
    /**
     * 是否启用 (1:启用, 0:禁用)
     */
    @Column(name = "IS_ENABLED", columnDefinition = "NUMBER(1)")
    @TableField("IS_ENABLED")
    private Boolean isEnabled = true;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 创建用户
     */
    @Column(name = "CREATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("CREATE_USER")
    private String createUser;
    
    /**
     * 更新用户
     */
    @Column(name = "UPDATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("UPDATE_USER")
    private String updateUser;
    
    /**
     * 备注说明
     */
    @Column(name = "REMARKS", columnDefinition = "VARCHAR2(500)")
    @TableField("REMARKS")
    private String remarks;
}
