<template>
  <div class="color-label-designer">
    <!-- 设计器头部 -->
    <div class="designer-header">
      <div class="header-title">
        <h3>彩色标签设计器</h3>
        <el-tag v-if="currentCoin" type="info" size="small">
          {{ currentCoin.coinNumber }} - {{ currentCoin.coinName }}
        </el-tag>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="View" 
          @click="showPreview = !showPreview"
          size="small"
        >
          {{ showPreview ? '隐藏预览' : '显示预览' }}
        </el-button>
        <el-button 
          type="success" 
          :icon="Check" 
          @click="handleSave"
          :loading="saving"
          size="small"
        >
          保存配置
        </el-button>
      </div>
    </div>

    <div class="designer-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-card shadow="never" class="config-card">
          <template #header>
            <span class="card-title">文字设置</span>
          </template>
          
          <!-- 标签文字 -->
          <el-form-item label="标签文字" class="form-item">
            <el-input
              v-model="config.labelText"
              placeholder="请输入彩色标签文字"
              maxlength="20"
              show-word-limit
              @input="handleConfigChange"
            />
          </el-form-item>

          <!-- 文字颜色 -->
          <el-form-item label="文字颜色" class="form-item">
            <div class="color-picker-wrapper">
              <el-color-picker
                v-model="config.textColor"
                @change="handleConfigChange"
                show-alpha
                :predefine="predefineColors"
              />
              <el-input
                v-model="config.textColor"
                placeholder="#FF0000"
                class="color-input"
                @input="handleConfigChange"
              />
            </div>
          </el-form-item>

          <!-- 字体设置 -->
          <el-form-item label="字体大小" class="form-item">
            <el-input-number
              v-model="config.fontSize"
              :min="12"
              :max="72"
              :step="2"
              @change="handleConfigChange"
              style="width: 100%"
            />
            <span class="unit">px</span>
          </el-form-item>

          <el-form-item label="字体名称" class="form-item">
            <el-select
              v-model="config.fontFamily"
              @change="handleConfigChange"
              style="width: 100%"
            >
              <el-option label="微软雅黑" value="微软雅黑" />
              <el-option label="宋体" value="宋体" />
              <el-option label="黑体" value="黑体" />
              <el-option label="楷体" value="楷体" />
              <el-option label="Arial" value="Arial" />
              <el-option label="Times New Roman" value="Times New Roman" />
            </el-select>
          </el-form-item>

          <el-form-item label="字体粗细" class="form-item">
            <el-select
              v-model="config.fontWeight"
              @change="handleConfigChange"
              style="width: 100%"
            >
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="细体" value="lighter" />
            </el-select>
          </el-form-item>
        </el-card>

        <el-card shadow="never" class="config-card">
          <template #header>
            <span class="card-title">位置设置</span>
          </template>

          <!-- 位置坐标 -->
          <el-form-item label="X坐标" class="form-item">
            <el-input-number
              v-model="config.positionX"
              :min="0"
              :max="200"
              :step="0.1"
              :precision="1"
              @change="handleConfigChange"
              style="width: 100%"
            />
            <span class="unit">mm</span>
          </el-form-item>

          <el-form-item label="Y坐标" class="form-item">
            <el-input-number
              v-model="config.positionY"
              :min="0"
              :max="200"
              :step="0.1"
              :precision="1"
              @change="handleConfigChange"
              style="width: 100%"
            />
            <span class="unit">mm</span>
          </el-form-item>

          <!-- 尺寸设置 -->
          <el-form-item label="宽度" class="form-item">
            <el-input-number
              v-model="config.width"
              :min="10"
              :max="100"
              :step="0.1"
              :precision="1"
              @change="handleConfigChange"
              style="width: 100%"
            />
            <span class="unit">mm</span>
          </el-form-item>

          <el-form-item label="高度" class="form-item">
            <el-input-number
              v-model="config.height"
              :min="5"
              :max="50"
              :step="0.1"
              :precision="1"
              @change="handleConfigChange"
              style="width: 100%"
            />
            <span class="unit">mm</span>
          </el-form-item>

          <!-- 快捷位置按钮 -->
          <div class="quick-position">
            <el-button-group>
              <el-button size="small" @click="setQuickPosition('top-left')">左上</el-button>
              <el-button size="small" @click="setQuickPosition('top-right')">右上</el-button>
              <el-button size="small" @click="setQuickPosition('bottom-left')">左下</el-button>
              <el-button size="small" @click="setQuickPosition('bottom-right')">右下</el-button>
            </el-button-group>
          </div>
        </el-card>
      </div>

      <!-- 右侧预览区域 -->
      <div v-if="showPreview" class="preview-panel">
        <el-card shadow="never" class="preview-card">
          <template #header>
            <span class="card-title">实时预览</span>
          </template>
          
          <div class="preview-container">
            <!-- 钱币标签底图 -->
            <div class="coin-label-base" :style="coinLabelStyle">
              <div class="coin-label-content">
                <!-- 这里显示钱币标签的基础内容 -->
                <div class="coin-info">
                  <div class="bank-name">中国人民银行</div>
                  <div class="coin-name">{{ currentCoin?.coinName || '钱币名称' }}</div>
                  <div class="coin-number">{{ currentCoin?.coinNumber || 'S/N 000000' }}</div>
                </div>
              </div>
              
              <!-- 彩色标签叠加层 -->
              <div 
                class="color-label-overlay"
                :style="colorLabelStyle"
                v-if="config.labelText"
              >
                {{ config.labelText }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { View, Check } from '@element-plus/icons-vue';
import { saveColorLabelConfig, getDefaultColorLabelConfig } from '../api';

const props = defineProps({
  coinData: {
    type: Object,
    default: null
  },
  templateType: {
    type: String,
    default: 'default'
  }
});

const emit = defineEmits(['save-success', 'config-change']);

// 响应式数据
const showPreview = ref(true);
const saving = ref(false);
const currentCoin = ref(props.coinData);

// 彩色标签配置
const config = reactive({
  id: null,
  coinId: '',
  labelText: '',
  textColor: '#FF0000',
  fontSize: 40,
  fontFamily: '微软雅黑',
  fontWeight: 'normal',
  positionX: 35.0,
  positionY: 6.2,
  width: 45.0,
  height: 20.0,
  templateType: props.templateType,
  isEnabled: true
});

// 预定义颜色
const predefineColors = ref([
  '#FF0000', '#FF6600', '#FFCC00', '#00FF00',
  '#0066FF', '#6600FF', '#FF0066', '#000000'
]);

// 计算属性
const coinLabelStyle = computed(() => ({
  width: '130mm',
  height: '26mm',
  border: '1px solid #ddd',
  position: 'relative',
  backgroundColor: '#fff',
  padding: '2mm'
}));

const colorLabelStyle = computed(() => ({
  position: 'absolute',
  left: `${config.positionX}mm`,
  top: `${config.positionY}mm`,
  width: `${config.width}mm`,
  height: `${config.height}mm`,
  color: config.textColor,
  fontSize: `${config.fontSize}px`,
  fontFamily: config.fontFamily,
  fontWeight: config.fontWeight,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  border: '1px dashed #ccc',
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  zIndex: 10
}));

// 方法
const handleConfigChange = () => {
  emit('config-change', { ...config });
};

const handleSave = async () => {
  if (!config.labelText?.trim()) {
    ElMessage.warning('请输入标签文字');
    return;
  }

  if (!currentCoin.value?.id) {
    ElMessage.warning('请选择要设置的钱币');
    return;
  }

  saving.value = true;
  try {
    config.coinId = currentCoin.value.id;
    const result = await saveColorLabelConfig(config);
    ElMessage.success('保存成功');
    emit('save-success', result);
  } catch (error) {
    ElMessage.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

const setQuickPosition = (position) => {
  const positions = {
    'top-left': { x: 5, y: 2 },
    'top-right': { x: 80, y: 2 },
    'bottom-left': { x: 5, y: 18 },
    'bottom-right': { x: 80, y: 18 }
  };
  
  if (positions[position]) {
    config.positionX = positions[position].x;
    config.positionY = positions[position].y;
    handleConfigChange();
  }
};

const loadDefaultConfig = async () => {
  try {
    const defaultConfig = await getDefaultColorLabelConfig(props.templateType);
    if (defaultConfig) {
      Object.assign(config, defaultConfig);
    }
  } catch (error) {
    console.warn('加载默认配置失败:', error);
  }
};

// 监听器
watch(() => props.coinData, (newCoin) => {
  currentCoin.value = newCoin;
  if (newCoin) {
    config.coinId = newCoin.id;
  }
});

watch(() => props.templateType, (newType) => {
  config.templateType = newType;
  loadDefaultConfig();
});

// 生命周期
onMounted(() => {
  loadDefaultConfig();
});

// 暴露方法给父组件
defineExpose({
  getConfig: () => ({ ...config }),
  setConfig: (newConfig) => Object.assign(config, newConfig),
  resetConfig: () => loadDefaultConfig()
});
</script>

<style scoped>
.color-label-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.designer-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.config-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.preview-panel {
  flex: 1;
  overflow: hidden;
}

.config-card,
.preview-card {
  height: fit-content;
}

.card-title {
  font-weight: 600;
  color: #333;
}

.form-item {
  margin-bottom: 16px;
}

.form-item :deep(.el-form-item__label) {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-input {
  flex: 1;
}

.unit {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.quick-position {
  margin-top: 12px;
}

.preview-container {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: auto;
}

.coin-label-base {
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coin-label-content {
  padding: 4mm;
}

.coin-info {
  font-family: '微软雅黑';
}

.bank-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 2px;
}

.coin-name {
  font-size: 12px;
  margin-bottom: 2px;
}

.coin-number {
  font-size: 10px;
  color: #666;
}

.color-label-overlay {
  cursor: move;
  user-select: none;
  transition: all 0.2s ease;
}

.color-label-overlay:hover {
  border-color: #409eff;
  box-shadow: 0 0 4px rgba(64, 158, 255, 0.3);
}
</style>
