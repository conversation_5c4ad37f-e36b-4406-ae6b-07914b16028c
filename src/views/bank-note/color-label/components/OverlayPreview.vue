<template>
  <div class="overlay-preview">
    <div class="preview-header">
      <div class="header-left">
        <h4>叠加预览</h4>
        <el-tag v-if="previewData.length > 0" type="info" size="small">
          共 {{ previewData.length }} 个标签
        </el-tag>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button 
            :icon="ZoomOut" 
            @click="handleZoom(0.8)"
            :disabled="scale <= 0.5"
            size="small"
          >
            缩小
          </el-button>
          <el-button 
            :icon="ZoomIn" 
            @click="handleZoom(1.2)"
            :disabled="scale >= 2"
            size="small"
          >
            放大
          </el-button>
          <el-button 
            :icon="RefreshRight" 
            @click="resetZoom"
            size="small"
          >
            重置
          </el-button>
        </el-button-group>
        <el-button 
          type="primary" 
          :icon="Printer" 
          @click="handlePrint"
          :loading="printing"
          size="small"
        >
          打印
        </el-button>
      </div>
    </div>

    <div class="preview-content" v-loading="loading">
      <div class="preview-container" :style="containerStyle">
        <div 
          v-for="(item, index) in previewData" 
          :key="item.coinId"
          class="preview-item"
          :style="getItemStyle(index)"
        >
          <!-- 钱币标签底图 -->
          <div class="coin-label-base" :style="coinLabelBaseStyle">
            <!-- 钱币基础信息 -->
            <div class="coin-info">
              <div class="bank-name">中国人民银行</div>
              <div class="coin-name">{{ item.coinName }}</div>
              <div class="coin-number">{{ item.coinNumber }}</div>
              <div class="coin-details">
                <span class="grade">{{ item.grade || '68' }}</span>
                <span class="grade-desc">{{ item.gradeDescription || 'Superb Gem Unc' }}</span>
              </div>
            </div>

            <!-- 彩色标签叠加 -->
            <div 
              v-if="item.colorLabelData && item.colorLabelData.isEnabled"
              class="color-label-overlay"
              :style="getColorLabelStyle(item.colorLabelData)"
              @mousedown="startDrag($event, item)"
            >
              {{ item.colorLabelData.labelText }}
            </div>
          </div>

          <!-- 标签信息 -->
          <div class="item-info">
            <span class="coin-id">{{ item.coinNumber }}</span>
            <span v-if="item.colorLabelData?.labelText" class="color-text">
              {{ item.colorLabelData.labelText }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置调整面板 -->
    <div v-if="selectedItem" class="position-panel">
      <el-card shadow="never">
        <template #header>
          <span>位置调整 - {{ selectedItem.coinNumber }}</span>
          <el-button 
            type="text" 
            @click="selectedItem = null"
            style="float: right; padding: 3px 0"
          >
            关闭
          </el-button>
        </template>
        
        <el-form :model="adjustForm" label-width="60px" size="small">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="X坐标">
                <el-input-number
                  v-model="adjustForm.positionX"
                  :min="0"
                  :max="200"
                  :step="0.1"
                  :precision="1"
                  @change="handlePositionChange"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Y坐标">
                <el-input-number
                  v-model="adjustForm.positionY"
                  :min="0"
                  :max="200"
                  :step="0.1"
                  :precision="1"
                  @change="handlePositionChange"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="savePosition" size="small">
              保存位置
            </el-button>
            <el-button @click="resetPosition" size="small">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { ZoomIn, ZoomOut, RefreshRight, Printer } from '@element-plus/icons-vue';
import { updateColorLabelConfig } from '../api';

const props = defineProps({
  previewData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['print', 'position-change']);

// 响应式数据
const scale = ref(1);
const printing = ref(false);
const selectedItem = ref(null);
const dragState = reactive({
  isDragging: false,
  startX: 0,
  startY: 0,
  startPosX: 0,
  startPosY: 0,
  currentItem: null
});

// 位置调整表单
const adjustForm = reactive({
  positionX: 0,
  positionY: 0
});

// 计算属性
const containerStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: 'top left',
  transition: 'transform 0.3s ease'
}));

const coinLabelBaseStyle = computed(() => ({
  width: '130mm',
  height: '26mm',
  border: '1px solid #ddd',
  backgroundColor: '#fff',
  position: 'relative',
  margin: '10mm',
  padding: '2mm',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
}));

// 方法
const getItemStyle = (index) => {
  const itemsPerRow = 2; // 每行显示2个
  const row = Math.floor(index / itemsPerRow);
  const col = index % itemsPerRow;
  
  return {
    position: 'absolute',
    left: `${col * 160}mm`,
    top: `${row * 50}mm`
  };
};

const getColorLabelStyle = (colorLabelData) => {
  if (!colorLabelData) return {};
  
  return {
    position: 'absolute',
    left: `${colorLabelData.positionX}mm`,
    top: `${colorLabelData.positionY}mm`,
    width: `${colorLabelData.width}mm`,
    height: `${colorLabelData.height}mm`,
    color: colorLabelData.textColor,
    fontSize: `${colorLabelData.fontSize}px`,
    fontFamily: colorLabelData.fontFamily,
    fontWeight: colorLabelData.fontWeight,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    border: '1px dashed #409eff',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    cursor: 'move',
    userSelect: 'none',
    zIndex: 10
  };
};

const handleZoom = (factor) => {
  scale.value = Math.max(0.5, Math.min(2, scale.value * factor));
};

const resetZoom = () => {
  scale.value = 1;
};

const handlePrint = () => {
  if (props.previewData.length === 0) {
    ElMessage.warning('没有可打印的数据');
    return;
  }
  
  printing.value = true;
  emit('print', props.previewData);
  
  // 模拟打印过程
  setTimeout(() => {
    printing.value = false;
  }, 2000);
};

// 拖拽相关方法
const startDrag = (event, item) => {
  if (!item.colorLabelData) return;
  
  dragState.isDragging = true;
  dragState.startX = event.clientX;
  dragState.startY = event.clientY;
  dragState.startPosX = parseFloat(item.colorLabelData.positionX);
  dragState.startPosY = parseFloat(item.colorLabelData.positionY);
  dragState.currentItem = item;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  
  event.preventDefault();
};

const handleDrag = (event) => {
  if (!dragState.isDragging || !dragState.currentItem) return;
  
  const deltaX = (event.clientX - dragState.startX) / scale.value;
  const deltaY = (event.clientY - dragState.startY) / scale.value;
  
  // 转换像素到毫米 (大约 3.78 像素 = 1 毫米)
  const mmPerPixel = 0.264583;
  const newX = Math.max(0, dragState.startPosX + deltaX * mmPerPixel);
  const newY = Math.max(0, dragState.startPosY + deltaY * mmPerPixel);
  
  dragState.currentItem.colorLabelData.positionX = newX;
  dragState.currentItem.colorLabelData.positionY = newY;
};

const stopDrag = () => {
  if (dragState.isDragging && dragState.currentItem) {
    // 发送位置变更事件
    emit('position-change', {
      coinId: dragState.currentItem.coinId,
      positionX: dragState.currentItem.colorLabelData.positionX,
      positionY: dragState.currentItem.colorLabelData.positionY
    });
  }
  
  dragState.isDragging = false;
  dragState.currentItem = null;
  
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 位置调整面板相关方法
const showPositionPanel = (item) => {
  selectedItem.value = item;
  if (item.colorLabelData) {
    adjustForm.positionX = parseFloat(item.colorLabelData.positionX);
    adjustForm.positionY = parseFloat(item.colorLabelData.positionY);
  }
};

const handlePositionChange = () => {
  if (selectedItem.value && selectedItem.value.colorLabelData) {
    selectedItem.value.colorLabelData.positionX = adjustForm.positionX;
    selectedItem.value.colorLabelData.positionY = adjustForm.positionY;
  }
};

const savePosition = async () => {
  if (!selectedItem.value || !selectedItem.value.colorLabelData) return;
  
  try {
    const config = {
      ...selectedItem.value.colorLabelData,
      positionX: adjustForm.positionX,
      positionY: adjustForm.positionY
    };
    
    await updateColorLabelConfig(config.configId, config);
    
    emit('position-change', {
      coinId: selectedItem.value.coinId,
      positionX: adjustForm.positionX,
      positionY: adjustForm.positionY
    });
    
    ElMessage.success('位置保存成功');
    selectedItem.value = null;
  } catch (error) {
    ElMessage.error('保存失败：' + error.message);
  }
};

const resetPosition = () => {
  if (selectedItem.value && selectedItem.value.colorLabelData) {
    adjustForm.positionX = 35.0;
    adjustForm.positionY = 6.2;
    handlePositionChange();
  }
};

// 监听器
watch(() => props.previewData, () => {
  selectedItem.value = null;
});

// 暴露方法给父组件
defineExpose({
  showPositionPanel,
  resetZoom,
  setScale: (newScale) => { scale.value = newScale; }
});
</script>

<style scoped>
.overlay-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.preview-content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
}

.preview-container {
  position: relative;
  min-width: 400mm;
  min-height: 300mm;
  padding: 20mm;
}

.preview-item {
  position: relative;
}

.coin-info {
  font-family: '微软雅黑';
  line-height: 1.4;
}

.bank-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 2px;
}

.coin-name {
  font-size: 12px;
  margin-bottom: 2px;
}

.coin-number {
  font-size: 10px;
  color: #666;
  margin-bottom: 4px;
}

.coin-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.grade {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.grade-desc {
  font-size: 9px;
  color: #666;
}

.color-label-overlay {
  transition: all 0.2s ease;
}

.color-label-overlay:hover {
  border-color: #409eff;
  box-shadow: 0 0 6px rgba(64, 158, 255, 0.4);
}

.item-info {
  margin-top: 4px;
  text-align: center;
  font-size: 12px;
}

.coin-id {
  display: block;
  font-weight: bold;
  color: #333;
}

.color-text {
  display: block;
  color: #666;
  margin-top: 2px;
}

.position-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 300px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
