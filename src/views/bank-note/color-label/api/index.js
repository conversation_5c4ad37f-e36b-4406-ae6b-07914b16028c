import request from '@/utils/request';

/**
 * 根据钱币ID获取彩色标签配置
 * @param {string} coinId - 钱币ID
 */
export async function getColorLabelConfig(coinId) {
  const res = await request.get(`/color-label/config/${coinId}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量获取彩色标签配置
 * @param {Array} coinIds - 钱币ID列表
 */
export async function getBatchColorLabelConfigs(coinIds) {
  const res = await request.post('/color-label/config/batch', { coinIds });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存彩色标签配置
 * @param {Object} config - 彩色标签配置
 */
export async function saveColorLabelConfig(config) {
  const res = await request.post('/color-label/config', config);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 更新彩色标签配置
 * @param {string} id - 配置ID
 * @param {Object} config - 彩色标签配置
 */
export async function updateColorLabelConfig(id, config) {
  const res = await request.put(`/color-label/config/${id}`, config);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除彩色标签配置
 * @param {string} coinId - 钱币ID
 */
export async function deleteColorLabelConfig(coinId) {
  const res = await request.delete(`/color-label/config/${coinId}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量设置彩色标签
 * @param {Object} params - 批量设置参数
 */
export async function batchSetColorLabels(params) {
  const res = await request.post('/color-label/batch-set', params);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 复制彩色标签配置
 * @param {string} sourceCoinId - 源钱币ID
 * @param {Array} targetCoinIds - 目标钱币ID列表
 */
export async function copyColorLabelConfig(sourceCoinId, targetCoinIds) {
  const res = await request.post('/color-label/copy', {
    sourceCoinId,
    targetCoinIds
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 生成预览数据
 * @param {Array} coinIds - 钱币ID列表
 * @param {string} templateId - 模板ID
 */
export async function generateColorLabelPreview(coinIds, templateId) {
  const res = await request.post('/color-label/preview', {
    coinIds,
    templateId
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 生成一体化打印数据
 * @param {Array} coinIds - 钱币ID列表
 * @param {string} templateId - 模板ID
 */
export async function generateIntegratedPrintData(coinIds, templateId) {
  const res = await request.post('/color-label/integrated-print', {
    coinIds,
    templateId
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取默认配置
 * @param {string} templateType - 模板类型
 */
export async function getDefaultColorLabelConfig(templateType) {
  const res = await request.get('/color-label/default-config', {
    params: { templateType }
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取推荐位置
 * @param {string} templateType - 模板类型
 */
export async function getRecommendedPosition(templateType) {
  const res = await request.get('/color-label/recommended-position', {
    params: { templateType }
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
