<template>
  <div class="color-label-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>彩色标签打印</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>钱币管理</el-breadcrumb-item>
          <el-breadcrumb-item>彩色标签打印</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          :icon="Plus" 
          @click="showBatchSetDialog = true"
          :disabled="selectedCoins.length === 0"
        >
          批量设置
        </el-button>
        <el-button 
          type="success" 
          :icon="View" 
          @click="handlePreview"
          :disabled="selectedCoins.length === 0"
        >
          预览打印
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 左侧钱币列表 -->
      <div class="coin-list-panel">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>钱币列表</span>
              <el-button 
                type="text" 
                @click="loadCoinList"
                :loading="loading"
                :icon="Refresh"
              >
                刷新
              </el-button>
            </div>
          </template>

          <!-- 搜索栏 -->
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索钱币编号或名称"
              :prefix-icon="Search"
              @input="handleSearch"
              clearable
            />
          </div>

          <!-- 钱币列表 -->
          <div class="coin-list" v-loading="loading">
            <el-checkbox-group v-model="selectedCoins" @change="handleSelectionChange">
              <div 
                v-for="coin in filteredCoins" 
                :key="coin.id"
                class="coin-item"
                :class="{ 'has-color-label': coin.hasColorLabel }"
                @click="handleCoinClick(coin)"
              >
                <el-checkbox :label="coin.id" @click.stop />
                <div class="coin-info">
                  <div class="coin-number">{{ coin.coinNumber }}</div>
                  <div class="coin-name">{{ coin.coinName }}</div>
                  <div class="coin-status">
                    <el-tag 
                      v-if="coin.hasColorLabel" 
                      type="success" 
                      size="small"
                    >
                      已设置
                    </el-tag>
                    <el-tag 
                      v-else 
                      type="info" 
                      size="small"
                    >
                      未设置
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.size"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadCoinList"
              @current-change="loadCoinList"
            />
          </div>
        </el-card>
      </div>

      <!-- 右侧设计器 -->
      <div class="designer-panel">
        <ColorLabelDesigner
          ref="designerRef"
          :coin-data="currentCoin"
          :template-type="currentTemplateType"
          @save-success="handleSaveSuccess"
          @config-change="handleConfigChange"
        />
      </div>
    </div>

    <!-- 批量设置对话框 -->
    <el-dialog
      v-model="showBatchSetDialog"
      title="批量设置彩色标签"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="标签文字" required>
          <el-input
            v-model="batchForm.labelText"
            placeholder="请输入彩色标签文字"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="文字颜色">
          <el-color-picker
            v-model="batchForm.textColor"
            show-alpha
            :predefine="predefineColors"
          />
        </el-form-item>

        <el-form-item label="字体大小">
          <el-input-number
            v-model="batchForm.fontSize"
            :min="12"
            :max="72"
            :step="2"
          />
          <span style="margin-left: 8px; color: #999;">px</span>
        </el-form-item>

        <el-form-item label="字体名称">
          <el-select v-model="batchForm.fontFamily" style="width: 100%">
            <el-option label="微软雅黑" value="微软雅黑" />
            <el-option label="宋体" value="宋体" />
            <el-option label="黑体" value="黑体" />
            <el-option label="楷体" value="楷体" />
          </el-select>
        </el-form-item>

        <el-form-item label="模板类型">
          <el-select v-model="batchForm.templateType" style="width: 100%">
            <el-option label="默认模板" value="default" />
            <el-option label="中乾模板" value="中乾模板" />
            <el-option label="金币模板" value="金币模板" />
          </el-select>
        </el-form-item>

        <el-alert
          :title="`将为 ${selectedCoins.length} 个钱币设置彩色标签`"
          type="info"
          :closable="false"
          style="margin-top: 16px"
        />
      </el-form>

      <template #footer>
        <el-button @click="showBatchSetDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleBatchSet"
          :loading="batchSetting"
        >
          确定设置
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="彩色标签预览"
      width="90%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <OverlayPreview
        :preview-data="previewData"
        :loading="previewLoading"
        @print="handlePrint"
        @position-change="handlePositionChange"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, View, Refresh, Search } from '@element-plus/icons-vue';

// 组件导入
import ColorLabelDesigner from './components/ColorLabelDesigner.vue';
import OverlayPreview from './components/OverlayPreview.vue';

// API导入
import {
  batchSetColorLabels,
  getBatchColorLabelConfigs,
  generateColorLabelPreview,
  generateIntegratedPrintData
} from './api';

// 响应式数据
const loading = ref(false);
const batchSetting = ref(false);
const previewLoading = ref(false);
const searchKeyword = ref('');
const selectedCoins = ref([]);
const currentCoin = ref(null);
const currentTemplateType = ref('default');

// 对话框状态
const showBatchSetDialog = ref(false);
const showPreviewDialog = ref(false);

// 钱币列表和分页
const coinList = ref([]);
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
});

// 批量设置表单
const batchForm = reactive({
  labelText: '',
  textColor: '#FF0000',
  fontSize: 40,
  fontFamily: '微软雅黑',
  fontWeight: 'normal',
  templateType: 'default'
});

// 预览数据
const previewData = ref([]);

// 预定义颜色
const predefineColors = ref([
  '#FF0000', '#FF6600', '#FFCC00', '#00FF00',
  '#0066FF', '#6600FF', '#FF0066', '#000000'
]);

// 组件引用
const designerRef = ref(null);

// 计算属性
const filteredCoins = computed(() => {
  if (!searchKeyword.value) return coinList.value;
  
  const keyword = searchKeyword.value.toLowerCase();
  return coinList.value.filter(coin => 
    coin.coinNumber.toLowerCase().includes(keyword) ||
    coin.coinName.toLowerCase().includes(keyword)
  );
});

// 方法
const loadCoinList = async () => {
  loading.value = true;
  try {
    // TODO: 调用实际的钱币列表API
    // 这里使用模拟数据
    const mockData = {
      records: [
        {
          id: '1',
          coinNumber: 'S/N 001',
          coinName: '1980年贰角',
          hasColorLabel: false
        },
        {
          id: '2',
          coinNumber: 'S/N 002',
          coinName: '1990年壹圆',
          hasColorLabel: true
        }
      ],
      total: 2
    };
    
    coinList.value = mockData.records;
    pagination.total = mockData.total;
    
    // 加载彩色标签配置状态
    await loadColorLabelStatus();
  } catch (error) {
    ElMessage.error('加载钱币列表失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const loadColorLabelStatus = async () => {
  try {
    const coinIds = coinList.value.map(coin => coin.id);
    const configs = await getBatchColorLabelConfigs(coinIds);
    
    // 更新钱币的彩色标签状态
    coinList.value.forEach(coin => {
      coin.hasColorLabel = !!configs[coin.id];
    });
  } catch (error) {
    console.warn('加载彩色标签状态失败:', error);
  }
};

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
};

const handleSelectionChange = (selection) => {
  selectedCoins.value = selection;
};

const handleCoinClick = (coin) => {
  currentCoin.value = coin;
  // 可以在这里加载该钱币的彩色标签配置
};

const handleSaveSuccess = (config) => {
  ElMessage.success('彩色标签配置保存成功');
  // 更新钱币状态
  if (currentCoin.value) {
    currentCoin.value.hasColorLabel = true;
  }
  loadColorLabelStatus();
};

const handleConfigChange = (config) => {
  // 配置变更时的处理逻辑
  console.log('配置变更:', config);
};

const handleBatchSet = async () => {
  if (!batchForm.labelText.trim()) {
    ElMessage.warning('请输入标签文字');
    return;
  }

  batchSetting.value = true;
  try {
    await batchSetColorLabels({
      coinIds: selectedCoins.value,
      labelText: batchForm.labelText,
      textColor: batchForm.textColor,
      fontSize: batchForm.fontSize,
      fontFamily: batchForm.fontFamily,
      fontWeight: batchForm.fontWeight,
      templateType: batchForm.templateType
    });

    ElMessage.success('批量设置成功');
    showBatchSetDialog.value = false;
    loadColorLabelStatus();
  } catch (error) {
    ElMessage.error('批量设置失败：' + error.message);
  } finally {
    batchSetting.value = false;
  }
};

const handlePreview = async () => {
  if (selectedCoins.value.length === 0) {
    ElMessage.warning('请选择要预览的钱币');
    return;
  }

  previewLoading.value = true;
  showPreviewDialog.value = true;
  
  try {
    const data = await generateColorLabelPreview(selectedCoins.value, currentTemplateType.value);
    previewData.value = data;
  } catch (error) {
    ElMessage.error('生成预览失败：' + error.message);
    showPreviewDialog.value = false;
  } finally {
    previewLoading.value = false;
  }
};

const handlePrint = async (printData) => {
  try {
    const result = await generateIntegratedPrintData(
      printData.map(item => item.coinId),
      currentTemplateType.value
    );
    
    // TODO: 调用打印功能
    ElMessage.success('打印任务已提交');
  } catch (error) {
    ElMessage.error('打印失败：' + error.message);
  }
};

const handlePositionChange = (positionData) => {
  console.log('位置变更:', positionData);
  // 可以在这里实时保存位置变更
};

// 生命周期
onMounted(() => {
  loadCoinList();
});
</script>

<style scoped>
.color-label-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  color: #333;
}

.page-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.coin-list-panel {
  width: 350px;
  display: flex;
  flex-direction: column;
}

.designer-panel {
  flex: 1;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 16px;
}

.coin-list {
  flex: 1;
  overflow-y: auto;
  max-height: 500px;
}

.coin-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.coin-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.coin-item.has-color-label {
  border-left: 3px solid #67c23a;
}

.coin-info {
  flex: 1;
  margin-left: 12px;
}

.coin-number {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.coin-name {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.coin-status {
  display: flex;
  align-items: center;
}

.pagination {
  margin-top: 16px;
  text-align: center;
}
</style>
